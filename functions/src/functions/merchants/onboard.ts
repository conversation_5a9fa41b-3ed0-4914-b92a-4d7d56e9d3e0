import { APIGatewayProxyHandler } from "aws-lambda";
import { PayrixService } from "../../service/payrix.service.js";
import { validateOnboardingRequest } from "./schemas/onboarding.schema.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
// import { checkExistingMerchant } from "./utils/merchant-validation.js"; // Commented out as per user request
import { handlePayrixError, handleValidationError } from "./utils/error-handling.js";
import { createUserAccountIfRequested } from "./services/user-account.service.js";
import { buildSuccessResponse } from "./utils/response-builders.js";

interface VerificationFile {
  name: string;
  size: number;
  type: string;
  content: string; // Base64 encoded
}

interface VerificationUploadResult {
  success: boolean;
  noteId?: string;
  documentId?: string;
  error?: string;
}

async function processBankVerification(
  payrixService: PayrixService,
  entityId: string,
  verificationFile: VerificationFile,
  requestId: string,
  verificationMethod: string
): Promise<VerificationUploadResult> {
  if (verificationMethod !== "manual") {
    logger.info("Skipping bank verification upload - not manual verification", {
      requestId,
      entityId,
      verificationMethod,
    });
    return {
      success: true,
      noteId: undefined,
      documentId: undefined,
    };
  }

  if (!verificationFile) {
    logger.warn("Manual verification selected but no file provided", {
      requestId,
      entityId,
      verificationMethod,
    });
    throw new Error("Manual verification requires a file upload");
  }

  logger.info("Starting priority bank verification upload process for manual verification", {
    requestId,
    entityId,
    verificationMethod,
    fileName: verificationFile.name,
    fileSize: verificationFile.size,
    fileType: verificationFile.type,
  });

  try {
    // Step 1: Create a note for bank verification
    logger.info("Creating verification note in Payrix", { requestId, entityId });
    const noteResponse = await payrixService.createNote({
      entity: entityId,
      note: "Bank account verification - void check uploaded during onboarding",
      type: "bank_verification",
    });

    if (!noteResponse?.id) {
      throw new Error("Failed to create note - no ID returned");
    }

    logger.info("Verification note created successfully", {
      requestId,
      entityId,
      noteId: noteResponse.id,
    });

    // Step 2: Create note document with the uploaded file
    logger.info("Uploading verification document to Payrix", {
      requestId,
      entityId,
      noteId: noteResponse.id,
      fileName: verificationFile.name,
    });

    const fileBuffer = Buffer.from(verificationFile.content, "base64");
    const documentResponse = await payrixService.createNoteDocument({
      note: noteResponse.id,
      file: {
        filename: verificationFile.name,
        content: fileBuffer,
        contentType: verificationFile.type,
      },
      description: "Void check for bank account verification",
    });

    if (!documentResponse?.id) {
      throw new Error("Failed to create note document - no ID returned");
    }

    logger.info("Bank verification upload completed successfully - priority operation complete", {
      requestId,
      entityId,
      noteId: noteResponse.id,
      documentId: documentResponse.id,
      fileName: verificationFile.name,
      fileSize: verificationFile.size,
    });

    return {
      success: true,
      noteId: noteResponse.id,
      documentId: documentResponse.id,
    };
  } catch (error) {
    logger.error("Priority bank verification upload failed", {
      requestId,
      entityId,
      fileName: verificationFile.name,
      error,
    });
    throw error;
  }
}

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    const body = JSON.parse(event.body || "{}");
    const validation = validateOnboardingRequest(body);

    if (!validation.success || !validation.data) {
      return handleValidationError(validation.errors || ["Unknown validation error"], requestId);
    }

    const data = validation.data;

    if (!data.clientIp) {
      return handleValidationError(["Client IP is required"], requestId);
    }

    logger.info("Processing direct Payrix submission", {
      requestId,
      email: data.email,
      legalName: data.name,
      clientIp: data.clientIp,
    });

    // const merchantCheck = await checkExistingMerchant(data.email, data.ein, requestId);
    // if (merchantCheck.exists && merchantCheck.response) {
    //   return merchantCheck.response;
    // }

    const payrixService = new PayrixService();
    let payrixResponse: { id: string; [key: string]: unknown };
    let payrixEntityId: string;

    try {
      logger.info("Creating merchant in Payrix (direct integration)", { requestId });

      payrixResponse = await payrixService.createMerchant(data);
      payrixEntityId = payrixResponse?.id;

      if (!payrixEntityId) {
        throw new Error("Payrix response did not contain entity ID");
      }

      logger.info("Payrix merchant created successfully (direct integration)", {
        requestId,
        payrixEntityId,
      });

      let verificationUploadResult = null;

      if (data.bankVerification?.verificationMethod) {
        if (data.bankVerification.verificationMethod === "manual" && data.bankVerification.verificationFile) {
          try {
            logger.info("Processing bank verification upload (priority operation)", {
              requestId,
              payrixEntityId,
              verificationMethod: data.bankVerification.verificationMethod,
              fileName: data.bankVerification.verificationFile.name,
            });

            verificationUploadResult = await processBankVerification(
              payrixService,
              payrixEntityId,
              data.bankVerification.verificationFile,
              requestId,
              data.bankVerification.verificationMethod
            );

            logger.info("Bank verification upload completed successfully", {
              requestId,
              payrixEntityId,
              success: verificationUploadResult.success,
              noteId: verificationUploadResult.noteId,
              documentId: verificationUploadResult.documentId,
            });
          } catch (verificationError) {
            logger.error("Bank verification upload failed", {
              requestId,
              payrixEntityId,
              error: verificationError,
            });
            // Don't fail the entire onboarding if verification upload fails
            verificationUploadResult = {
              success: false,
              error: "Failed to upload verification document",
            };
          }
        } else if (data.bankVerification.verificationMethod === "plaid") {
          // Plaid verification - no file upload or Payrix API calls needed
          logger.info("Skipping bank verification upload - Plaid verification selected", {
            requestId,
            payrixEntityId,
            verificationMethod: data.bankVerification.verificationMethod,
          });
          verificationUploadResult = {
            success: true,
            noteId: undefined,
            documentId: undefined,
          };
        } else {
          // Unknown verification method or manual without file
          logger.warn("Invalid bank verification configuration", {
            requestId,
            payrixEntityId,
            verificationMethod: data.bankVerification.verificationMethod,
            hasFile: !!data.bankVerification.verificationFile,
          });
          verificationUploadResult = {
            success: false,
            error: "Invalid bank verification configuration",
          };
        }
      }

      let userAccountResult;
      try {
        logger.info("Processing user account creation (after bank verification)", {
          requestId,
          payrixEntityId,
        });

        userAccountResult = await createUserAccountIfRequested(data, payrixEntityId, requestId);

        if (userAccountResult.success) {
          logger.info("User account created successfully", {
            requestId,
            payrixEntityId,
            userId: userAccountResult.data?.id,
          });
        } else {
          logger.warn("User account creation failed, but merchant and bank verification completed", {
            requestId,
            payrixEntityId,
            error: userAccountResult.error,
          });
        }
      } catch (userAccountError) {
        logger.error("User account creation failed with exception", {
          requestId,
          payrixEntityId,
          error: userAccountError,
        });
        userAccountResult = {
          success: false,
          error: "User account creation failed",
        };
      }

      return buildSuccessResponse(
        data,
        payrixResponse,
        payrixEntityId,
        userAccountResult.success ? userAccountResult.data || null : null,
        verificationUploadResult
      );
    } catch (payrixError) {
      return handlePayrixError(payrixError as Error, requestId);
    }
  } catch (error) {
    const err = error as Error;
    logger.error("Error in onboard handler", {
      requestId,
      error: err.message,
      stack: err.stack,
    });

    return createErrorResponse(500, "Internal Server Error", err.message);
  }
};
