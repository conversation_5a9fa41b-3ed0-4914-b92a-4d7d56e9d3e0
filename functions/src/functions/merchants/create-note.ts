import { APIGatewayProxyHandler } from "aws-lambda";
import { PayrixService } from "../../service/payrix.service.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { z } from "zod";

const CreateNoteRequestSchema = z.object({
  entityId: z.string().min(1, "Entity ID is required"),
  note: z.string().min(1, "Note content is required"),
  type: z.string().optional(),
});

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    logger.info("Processing create note request", { requestId });

    if (!event.body) {
      return createErrorResponse(400, "Request body is required", requestId);
    }

    let requestData;
    try {
      requestData = JSON.parse(event.body);
    } catch (parseError) {
      logger.error("Invalid JSON in request body", { requestId, error: parseError });
      return createErrorResponse(400, "Invalid JSON in request body", requestId);
    }

    const validationResult = CreateNoteRequestSchema.safeParse(requestData);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => `${err.path.join(".")}: ${err.message}`);
      logger.error("Validation failed", { requestId, errors });
      return createErrorResponse(400, `Validation failed: ${errors.join(", ")}`, requestId);
    }

    const { entityId, note, type } = validationResult.data;

    const payrixService = new PayrixService();

    // Validate that the entity exists
    const merchantValidation = await payrixService.validateMerchantById(entityId);
    if (!merchantValidation.isValid) {
      logger.error("Invalid entity ID", { requestId, entityId });
      return createErrorResponse(404, "Entity not found", requestId);
    }

    // Create note via Payrix API
    const noteData = {
      entity: entityId,
      note,
      type: type || "bank_verification",
    };

    logger.info("Creating note in Payrix", { requestId, entityId, noteType: type });

    const payrixResponse = await payrixService.createNote(noteData);

    if (!payrixResponse?.id) {
      throw new Error("Payrix response did not contain note ID");
    }

    logger.info("Note created successfully", {
      requestId,
      noteId: payrixResponse.id,
      entityId,
    });

    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
      },
      body: JSON.stringify({
        success: true,
        message: "Note created successfully",
        data: {
          noteId: payrixResponse.id,
          entityId,
          note,
          type: type || "bank_verification",
          createdAt: new Date().toISOString(),
        },
      }),
    };
  } catch (error) {
    logger.error("Error creating note", { requestId, error });
    return createErrorResponse(500, "Internal server error", requestId);
  }
};
