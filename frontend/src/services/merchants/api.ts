import type { AxiosResponse } from "axios";
import { apiClient } from "../config";
import type {
  CreatePayrixMerchantRequest,
  MerchantResponse,
  CreateNoteRequest,
  CreateNoteResponse,
  CreateNoteDocumentRequest,
  CreateNoteDocumentResponse,
} from "../types/merchant";

export const createMerchant = async (merchantData: CreatePayrixMerchantRequest): Promise<MerchantResponse> => {
  try {
    const response: AxiosResponse<MerchantResponse> = await apiClient.post("/merchants/onboard", merchantData);
    return response.data;
  } catch (error) {
    console.error("Error creating merchant:", error);
    throw error;
  }
};

export const createNote = async (noteData: CreateNoteRequest): Promise<CreateNoteResponse> => {
  try {
    const response: AxiosResponse<CreateNoteResponse> = await apiClient.post("/merchants/notes", noteData);
    return response.data;
  } catch (error) {
    console.error("Error creating note:", error);
    throw error;
  }
};

export const createNoteDocument = async (documentData: CreateNoteDocumentRequest): Promise<CreateNoteDocumentResponse> => {
  try {
    const response: AxiosResponse<CreateNoteDocumentResponse> = await apiClient.post("/merchants/note-documents", documentData);
    return response.data;
  } catch (error) {
    console.error("Error creating note document:", error);
    throw error;
  }
};
