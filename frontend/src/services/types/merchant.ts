export interface CreateMerchantRequest {
  businessName: string;
  legalName: string;
  email: string;
  phone: string;
  website?: string;
}

export interface CreatePayrixMerchantRequest {
  name: string;
  dba?: string;
  ein: string;
  type: number;
  public: number;
  website: string;
  email: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone: string;
  customerPhone?: string;
  fax?: string;
  mcc: string;
  status: number;
  tcVersion: string;
  tcDate?: string; // Generated at submission time
  clientIp: string;
  currency: string;
  tcAttestation?: number;
  visaDisclosure?: number;
  disclosureIP?: string;
  disclosureDate?: string;
  merchantIp?: string;
  username: string;
  password: string;
  confirmPassword?: string;
  createAccount: boolean;
  annualCCSales?: number;
  avgTicket?: number;
  established?: string;
  accounts: {
    primary: number;
    currency: string;
    account: {
      method: number;
      number: string;
      routing: string;
    };
  }[];
  merchant: {
    dba?: string;
    new: number;
    mcc: string;
    status: string;
    annualCCSales?: number;
    avgTicket?: number;
    established?: string;
    members: {
      title: string;
      first: string;
      middle?: string;
      last: string;
      ssn?: string;
      dob: string;
      dl?: string;
      dlstate?: string;
      ownership: number;
      significantResponsibility: number;
      politicallyExposed: number;
      email: string;
      phone: string;
      fax?: string;
      primary: string;
      address1: string;
      address2?: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    }[];
  };
  bankVerification?: {
    verificationMethod: "plaid" | "manual";
    verificationFile?: {
      name: string;
      size: number;
      type: string;
      content: string; // Base64 encoded file content
    };
  };
  logins?: {
    username: string;
    password: string;
    first: string;
    last: string;
    email: string;
  }[];
}

export interface MerchantResponse {
  success: boolean;
  message: string;
  merchantId: string;
  payrixEntityId?: string;
  data?: {
    merchant: {
      merchant_id: string;
      legal_name: string;
      email: string;
      verification_status: number;
    };
    payrixResponse?: unknown;
  };
  error?: string;
  errors?: string[];
}

export interface CreateNoteRequest {
  entityId: string;
  note: string;
  type?: string;
}

export interface CreateNoteResponse {
  success: boolean;
  message: string;
  data: {
    noteId: string;
    entityId: string;
    note: string;
    type?: string;
    createdAt: string;
  };
}

export interface CreateNoteDocumentRequest {
  noteId: string;
  file: File;
  description?: string;
}

export interface CreateNoteDocumentResponse {
  success: boolean;
  message: string;
  data: {
    documentId: string;
    noteId: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    description?: string;
    uploadedAt: string;
  };
}
