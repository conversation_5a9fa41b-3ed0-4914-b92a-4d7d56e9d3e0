import { FileUpload } from "../form-fields";
import { VERIFICATION_METHODS, type VerificationMethod } from "../constants/bankAccountConstants";
import { BankAccountValidationErrors } from "../utils/validation";

interface BankVerificationSectionProps {
  verificationMethod: VerificationMethod;
  onVerificationMethodChange: (method: VerificationMethod) => void;
  uploadedFile: File | null;
  onFileChange: (file: File | null) => void;
  uploadPreview: string | null;
  onPreviewChange: (preview: string | null) => void;
  errors: BankAccountValidationErrors;
}

export const BankVerificationSection = ({
  verificationMethod,
  onVerificationMethodChange,
  uploadedFile,
  onFileChange,
  uploadPreview,
  onPreviewChange,
  errors,
}: BankVerificationSectionProps) => {
  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Bank Account Verification</h2>

      <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-6">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-amber-900 mb-1">Bank Verification Required</h3>
            <div className="text-sm text-amber-800 space-y-1">
              <p>• Your bank account will need to be verified before processing payments</p>
              <p>• Verification typically takes 1-2 business days</p>
              <p>• You can upload a void check now or verify manually later</p>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <label className="relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
          <input
            type="radio"
            name="verificationMethod"
            value={VERIFICATION_METHODS.PLAID}
            checked={verificationMethod === VERIFICATION_METHODS.PLAID}
            onChange={() => onVerificationMethodChange(VERIFICATION_METHODS.PLAID)}
            className="mt-1 text-blue-600 border-gray-300 focus:ring-blue-500"
            disabled
          />
          <div className="ml-3">
            <div className="flex items-center space-x-2">
              <span className="block text-sm font-medium text-gray-900">Verify with Plaid</span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">Coming Soon</span>
            </div>
            <span className="block text-sm text-gray-500">Instantly verify your bank account using Plaid&apos;s secure connection</span>
          </div>
        </label>

        <label className="relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
          <input
            type="radio"
            name="verificationMethod"
            value={VERIFICATION_METHODS.MANUAL}
            checked={verificationMethod === VERIFICATION_METHODS.MANUAL}
            onChange={() => onVerificationMethodChange(VERIFICATION_METHODS.MANUAL)}
            className="mt-1 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <div className="ml-3">
            <span className="block text-sm font-medium text-gray-900">Manual Verification</span>
            <span className="block text-sm text-gray-500">Upload a voided check for manual verification of your bank account</span>
          </div>
        </label>
      </div>

      {verificationMethod === VERIFICATION_METHODS.MANUAL && (
        <div className="mt-6">
          <FileUpload
            label="Void Check Upload"
            value={uploadedFile}
            onChange={onFileChange}
            preview={uploadPreview}
            onPreviewChange={onPreviewChange}
            error={errors.voidCheck}
            accept="image/*"
            maxSizeMB={10}
            placeholder="Upload a voided check"
            hint="PNG, JPG, GIF up to 10MB"
          />
        </div>
      )}
    </div>
  );
};
