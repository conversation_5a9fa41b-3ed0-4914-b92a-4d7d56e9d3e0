import { type CreatePayrixMerchantRequest } from "../../../services/api";
import { type ComplianceState } from "./reviewValidation";
import { PAYRIX_FIELD_MAPPING } from "../../../config/payrixFieldMapping";

type FormData = Partial<CreatePayrixMerchantRequest>;

export const generateTcDate = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hour = String(now.getHours()).padStart(2, "0");
  const minute = String(now.getMinutes()).padStart(2, "0");
  return `${year}${month}${day}${hour}${minute}`;
};

export const generateDisclosureDate = (): string => {
  return new Date().toISOString().slice(0, 10).replace(/-/g, "");
};

export const formatMemberDob = (dob: string): string => {
  if (!dob) return "";

  const dateStr = dob.replace(/\D/g, "");

  if (dateStr.length === 8) {
    const firstFour = dateStr.substring(0, 4);
    if (firstFour.startsWith("19") || firstFour.startsWith("20")) {
      return dateStr;
    } else {
      const month = dateStr.substring(0, 2);
      const day = dateStr.substring(2, 4);
      const year = dateStr.substring(4, 8);
      return `${year}${month}${day}`;
    }
  } else if (dob.includes("-") || dob.includes("/")) {
    const date = new Date(dob);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}${month}${day}`;
    }
  }

  return dateStr;
};

export const transformFormDataToPayrix = (formData: FormData, complianceState: ComplianceState, clientIp: string): CreatePayrixMerchantRequest => {
  return {
    // Top-level entity fields
    name: formData.name || "",
    address1: formData.address1 || "",
    address2: formData.address2,
    city: formData.city || "",
    state: formData.state || "",
    zip: formData.zip || "",
    country: formData.country || "USA",
    phone: formData.phone || "",
    email: formData.email || "",
    ein: formData.ein?.replace(/\D/g, "") || "",
    website: formData.website || "https://nowebsite.com",
    type: 2, // Always 2 for merchant
    public: formData.type === 2 ? 1 : 0, // Corporation = public, others = private
    mcc: formData.merchant?.mcc || "",
    status: 1, // Always set to 1 (ready to board) regardless of form input
    tcVersion: "072023",
    tcDate: generateTcDate(), // Generated at submission time when user accepts terms
    [PAYRIX_FIELD_MAPPING.CLIENT_IP]: clientIp,
    currency: "USD",
    tcAttestation: complianceState.tcAttestation ? 1 : 0,
    visaDisclosure: complianceState.visaDisclosure ? 1 : 0,
    [PAYRIX_FIELD_MAPPING.DISCLOSURE_IP]: clientIp,
    disclosureDate: generateDisclosureDate(),
    [PAYRIX_FIELD_MAPPING.MERCHANT_IP]: clientIp,

    // User account creation fields (mandatory)
    createAccount: true,
    username: formData.username || "",
    password: formData.password || "",

    // Bank account information
    accounts: [
      {
        primary: 1,
        currency: "USD",
        account: {
          method: formData.accounts?.[0]?.account?.method || 10,
          number: formData.accounts?.[0]?.account?.number?.replace(/\D/g, "") || "",
          routing: formData.accounts?.[0]?.account?.routing?.replace(/\D/g, "") || "",
        },
      },
    ],

    // Nested merchant object
    merchant: {
      dba: formData.merchant?.dba || formData.name || "",
      new: formData.merchant?.new ?? 1, // Use form value or default to 1 (first time)
      mcc: formData.merchant?.mcc || "",
      status: "1", // Always set to "1" (ready to board) regardless of form input
      annualCCSales: formData.merchant?.new === 1 ? 0 : formData.merchant?.annualCCSales || 0, // Force 0 if new=1
      avgTicket: formData.merchant?.avgTicket || 50, // Default $50 if not provided
      established: formData.merchant?.established,
      members:
        formData.merchant?.members?.map((member) => ({
          title: member.title || "",
          first: member.first || "",
          middle: member.middle || "",
          last: member.last || "",
          ssn: member.ssn?.replace(/\D/g, ""),
          dob: formatMemberDob(member.dob || ""),
          dl: member.dl,
          dlstate: member.dlstate,
          ownership: member.ownership || 10000,
          significantResponsibility: member.significantResponsibility || 1,
          politicallyExposed: member.politicallyExposed || 0,
          email: member.email || "",
          phone: member.phone?.replace(/\D/g, "") || "",
          primary: member.primary || "1",
          address1: member.address1 || "",
          address2: member.address2,
          city: member.city || "",
          state: member.state || "",
          zip: member.zip || "",
          country: member.country || "USA",
        })) || [],
    },

    // Login data for user account creation (when createAccount is true)
    ...(formData.createAccount &&
      formData.username &&
      formData.password && {
        logins: [
          {
            username: formData.username,
            password: formData.password,
            first: formData.merchant?.members?.find((m) => m.primary === "1")?.first || "",
            last: formData.merchant?.members?.find((m) => m.primary === "1")?.last || "",
            email: formData.merchant?.members?.find((m) => m.primary === "1")?.email || formData.email || "",
          },
        ],
      }),

    // Bank verification data
    bankVerification: formData.bankVerification,
  };
};
